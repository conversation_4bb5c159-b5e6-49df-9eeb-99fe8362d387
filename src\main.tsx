import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Production error detection and localStorage safety check
if (import.meta.env.PROD) {
  // Check localStorage availability early
  try {
    if (typeof localStorage === 'undefined' || !localStorage) {
      console.warn('localStorage is not available in production');
    } else {
      // Test localStorage functionality
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      console.log('localStorage is working correctly');
    }
  } catch (error) {
    console.error('localStorage test failed:', error);
  }

  window.addEventListener('error', (e) => {
    console.error('Production Error:', e.error);
    console.error('Error details:', {
      message: e.message,
      filename: e.filename,
      lineno: e.lineno,
      colno: e.colno,
      stack: e.error?.stack
    });
  });

  window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    console.error('Promise rejection details:', {
      reason: e.reason,
      stack: e.reason?.stack
    });
  });
}

const rootElement = document.getElementById("root");

// Remove loading indicator if it exists
const loadingIndicator = document.getElementById("loading-indicator");
if (loadingIndicator) {
  loadingIndicator.remove();
}

if (rootElement) {
  try {
    createRoot(rootElement).render(<App />);
    console.log('React app rendered successfully');
  } catch (error) {
    console.error('Failed to render React app:', error);
    // Fallback display
    rootElement.innerHTML = `
      <div style="padding: 20px; color: #e6cb8e; background: #0a0a0a; font-family: Arial, sans-serif; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
        <div style="text-align: center; max-width: 500px;">
          <h1>StreamDB</h1>
          <p>Application failed to load. Please refresh the page.</p>
          <p style="font-size: 12px; opacity: 0.7;">Error: ${error instanceof Error ? error.message : 'Unknown error'}</p>
          <button onclick="window.location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #e6cb8e; color: #0a0a0a; border: none; border-radius: 5px; cursor: pointer;">
            Reload Page
          </button>
        </div>
      </div>
    `;
  }
} else {
  console.error('Root element not found');
}
