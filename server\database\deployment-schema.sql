-- StreamDB Online - Deployment and Webhook Database Schema
-- This file creates the necessary tables for GitHub webhook deployment tracking

-- Table for storing deployment attempts and their status
CREATE TABLE IF NOT EXISTS deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status ENUM('STARTED', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'STARTED',
    branch VARCHAR(100) NOT NULL DEFAULT 'main',
    commits INT NOT NULL DEFAULT 0,
    pusher VARCHAR(100),
    repository VARCHAR(200),
    ip_address VARCHAR(45),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_branch (branch)
);

-- Table for storing detailed deployment logs
CREATE TABLE IF NOT EXISTS deployment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('INFO', 'WARNING', 'ERROR', 'SUCCESS', 'DEBUG') NOT NULL DEFAULT 'INFO',
    message TEXT NOT NULL,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
);

-- Table for storing webhook events (for debugging and monitoring)
CREATE TABLE IF NOT EXISTS webhook_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    repository VARCHAR(200),
    branch VARCHAR(100),
    commit_sha VARCHAR(40),
    pusher VARCHAR(100),
    payload JSON,
    signature_valid BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_repository (repository),
    INDEX idx_branch (branch),
    INDEX idx_created_at (created_at),
    INDEX idx_processed (processed)
);

-- Table for storing manual deployment requests from admin panel
CREATE TABLE IF NOT EXISTS manual_deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT,
    deployment_type ENUM('MANUAL', 'ROLLBACK', 'TEST') NOT NULL DEFAULT 'MANUAL',
    target_branch VARCHAR(100) NOT NULL DEFAULT 'main',
    status ENUM('PENDING', 'RUNNING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_admin_user (admin_user_id)
);

-- Table for storing deployment configuration and settings
CREATE TABLE IF NOT EXISTS deployment_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_config_key (config_key)
);

-- Insert default deployment configuration
INSERT INTO deployment_config (config_key, config_value, description) VALUES
('webhook_enabled', 'true', 'Enable/disable GitHub webhook processing'),
('auto_deploy_enabled', 'true', 'Enable/disable automatic deployments'),
('allowed_branch', 'main', 'Branch that triggers automatic deployment'),
('max_deployments_per_hour', '10', 'Maximum number of deployments allowed per hour'),
('deployment_timeout', '300', 'Deployment timeout in seconds'),
('backup_enabled', 'true', 'Enable/disable automatic backups before deployment'),
('notification_enabled', 'false', 'Enable/disable deployment notifications')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    description = VALUES(description);

-- View for recent deployment activity (last 30 days)
CREATE OR REPLACE VIEW recent_deployments AS
SELECT 
    d.id,
    d.status,
    d.branch,
    d.commits,
    d.pusher,
    d.repository,
    d.created_at,
    d.completed_at,
    TIMESTAMPDIFF(SECOND, d.created_at, COALESCE(d.completed_at, NOW())) as duration_seconds,
    d.error_message
FROM deployments d
WHERE d.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY d.created_at DESC;

-- View for deployment statistics
CREATE OR REPLACE VIEW deployment_stats AS
SELECT 
    COUNT(*) as total_deployments,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_deployments,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_deployments,
    SUM(CASE WHEN status = 'STARTED' THEN 1 ELSE 0 END) as running_deployments,
    ROUND(AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)), 2) as avg_duration_seconds,
    MAX(created_at) as last_deployment,
    COUNT(DISTINCT DATE(created_at)) as deployment_days
FROM deployments
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- View for recent logs with context
CREATE OR REPLACE VIEW recent_logs AS
SELECT 
    dl.id,
    dl.level,
    dl.message,
    dl.data,
    dl.created_at,
    d.id as deployment_id,
    d.status as deployment_status,
    d.branch,
    d.pusher
FROM deployment_logs dl
LEFT JOIN deployments d ON DATE(dl.created_at) = DATE(d.created_at) 
    AND ABS(TIMESTAMPDIFF(SECOND, dl.created_at, d.created_at)) <= 300
WHERE dl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY dl.created_at DESC;

-- Stored procedure for cleanup old logs (keep last 90 days)
DELIMITER //
CREATE PROCEDURE CleanupDeploymentLogs()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Clean up old deployment logs (keep last 90 days)
    DELETE FROM deployment_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- Clean up old webhook events (keep last 30 days)
    DELETE FROM webhook_events 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean up old deployments (keep last 180 days)
    DELETE FROM deployments 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 180 DAY);
    
    COMMIT;
    
    SELECT 
        'Cleanup completed successfully' as message,
        NOW() as timestamp;
END //
DELIMITER ;

-- Create indexes for better performance
CREATE INDEX idx_deployments_status_date ON deployments(status, created_at);
CREATE INDEX idx_logs_level_date ON deployment_logs(level, created_at);
CREATE INDEX idx_webhook_events_processed_date ON webhook_events(processed, created_at);

-- Grant permissions (adjust username as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.deployments TO 'dbadmin_streamdb'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.deployment_logs TO 'dbadmin_streamdb'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.webhook_events TO 'dbadmin_streamdb'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.manual_deployments TO 'dbadmin_streamdb'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON streamdb_database.deployment_config TO 'dbadmin_streamdb'@'localhost';
