#!/bin/bash

# StreamDB GitHub Webhook Auto-Deployment System Setup
# This script sets up the complete GitHub webhook deployment system

echo "🚀 StreamDB GitHub Webhook Auto-Deployment Setup"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

log_info "Step 1: Building the application..."
npm run build
if [ $? -ne 0 ]; then
    log_error "Build failed. Please check for errors."
    exit 1
fi
log_success "Application built successfully"

log_info "Step 2: Installing server dependencies..."
cd server
npm install --production
if [ $? -ne 0 ]; then
    log_error "Failed to install server dependencies"
    exit 1
fi
cd ..
log_success "Server dependencies installed"

log_info "Step 3: Setting up deployment database..."
# Create the database schema file if it doesn't exist
if [ ! -f "server/database/deployment-schema.sql" ]; then
    log_warning "Database schema file not found. Creating it..."
    mkdir -p server/database
    cat > server/database/deployment-schema.sql << 'EOF'
-- StreamDB Deployment Database Schema
CREATE TABLE IF NOT EXISTS deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status ENUM('STARTED', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'STARTED',
    branch VARCHAR(100) NOT NULL DEFAULT 'main',
    commits INT NOT NULL DEFAULT 0,
    pusher VARCHAR(100),
    repository VARCHAR(200),
    ip_address VARCHAR(45),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS deployment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('INFO', 'WARNING', 'ERROR', 'SUCCESS', 'DEBUG') NOT NULL DEFAULT 'INFO',
    message TEXT NOT NULL,
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_level (level),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS webhook_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    repository VARCHAR(200),
    branch VARCHAR(100),
    commit_sha VARCHAR(40),
    pusher VARCHAR(100),
    payload JSON,
    signature_valid BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);

INSERT INTO deployment_logs (level, message, data) VALUES 
('INFO', 'Deployment database schema initialized', JSON_OBJECT('timestamp', NOW()));
EOF
    log_success "Database schema file created"
fi

log_info "Step 4: Checking server configuration..."
cd server

# Check if .env file exists and has required variables
if [ ! -f ".env" ]; then
    log_error ".env file not found in server directory"
    log_info "Please create server/.env file with database credentials"
    exit 1
fi

# Check for required environment variables
if ! grep -q "WEBHOOK_SECRET=" .env; then
    log_warning "WEBHOOK_SECRET not found in .env file"
    log_info "Generating webhook secret..."
    WEBHOOK_SECRET=$(openssl rand -hex 32 2>/dev/null || head -c 32 /dev/urandom | xxd -p -c 32)
    echo "WEBHOOK_SECRET=$WEBHOOK_SECRET" >> .env
    log_success "Webhook secret generated and added to .env"
fi

if ! grep -q "GITHUB_REPO=" .env; then
    echo "GITHUB_REPO=aakash171088/Streaming_DB" >> .env
    log_success "GitHub repository added to .env"
fi

cd ..

log_info "Step 5: Testing webhook endpoint..."
cd server

# Start server in background for testing
log_info "Starting server for testing..."
NODE_ENV=production node index.js > ../webhook-test.log 2>&1 &
SERVER_PID=$!
sleep 5

# Test if server is running
if kill -0 $SERVER_PID 2>/dev/null; then
    log_success "Server started successfully (PID: $SERVER_PID)"
    
    # Test webhook endpoint
    if command -v curl &> /dev/null; then
        log_info "Testing webhook endpoint..."
        RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health)
        if [ "$RESPONSE" = "200" ]; then
            log_success "Webhook endpoint is accessible"
        else
            log_warning "Webhook endpoint returned status: $RESPONSE"
        fi
    else
        log_warning "curl not available for testing"
    fi
    
    # Stop test server
    kill $SERVER_PID 2>/dev/null
    wait $SERVER_PID 2>/dev/null
    log_info "Test server stopped"
else
    log_error "Failed to start server for testing"
fi

cd ..

log_info "Step 6: Setting up PM2 configuration..."
cd server

# Stop existing processes
pm2 stop streamdb-online 2>/dev/null || true
pm2 delete streamdb-online 2>/dev/null || true

# Start with PM2
log_info "Starting server with PM2..."
NODE_ENV=production pm2 start index.js --name streamdb-online --env production

if [ $? -eq 0 ]; then
    log_success "Server started with PM2"
    pm2 save
    log_success "PM2 configuration saved"
else
    log_error "Failed to start server with PM2"
    exit 1
fi

cd ..

log_info "Step 7: Final verification..."
sleep 3

# Check PM2 status
pm2 list | grep streamdb-online
if [ $? -eq 0 ]; then
    log_success "Server is running in PM2"
else
    log_error "Server not found in PM2"
fi

# Test final endpoint
if command -v curl &> /dev/null; then
    RESPONSE=$(curl -s http://localhost:3001/api/health)
    if echo "$RESPONSE" | grep -q "OK"; then
        log_success "Server health check passed"
    else
        log_warning "Server health check failed"
    fi
fi

echo ""
log_success "🎉 GitHub Webhook Auto-Deployment Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure GitHub webhook with URL: https://streamdb.online/api/webhook/github"
echo "2. Use the WEBHOOK_SECRET from server/.env file"
echo "3. Set content type to application/json"
echo "4. Select 'Just the push event'"
echo "5. Test the webhook by pushing to main branch"
echo ""
echo "🔧 Management Commands:"
echo "• Check status: pm2 status"
echo "• View logs: pm2 logs streamdb-online"
echo "• Restart: pm2 restart streamdb-online"
echo "• Admin panel: https://streamdb.online/admin (GitHub Deployment tab)"
echo ""
echo "📊 Webhook URL: https://streamdb.online/api/webhook/github"
echo "🔐 Webhook Secret: Check server/.env file"
echo ""

# Show webhook secret for GitHub configuration
WEBHOOK_SECRET=$(grep "WEBHOOK_SECRET=" server/.env | cut -d'=' -f2)
if [ ! -z "$WEBHOOK_SECRET" ]; then
    echo "🔑 Your webhook secret (for GitHub configuration):"
    echo "$WEBHOOK_SECRET"
    echo ""
fi

log_success "Setup completed successfully! Your auto-deployment system is ready."
