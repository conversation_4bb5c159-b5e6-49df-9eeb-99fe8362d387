# localStorage to Database Migration Guide

## Overview

This migration completely replaces all localStorage usage with secure MySQL database storage, providing:

- **Enhanced Security**: No sensitive data stored client-side
- **Cross-device Synchronization**: Data persists across devices and sessions
- **Better Performance**: Reduced client-side storage limitations
- **Audit Trail**: Complete tracking of user interactions
- **Scalability**: Database-backed storage for production environments

## Migration Summary

### 1. Ad Blocker Awareness Tracking
**Before**: Stored in localStorage with key `adBlockerAwarenessShown`
**After**: Stored in `ad_blocker_tracking` database table

**Changes**:
- `shouldShowAwarenessPopup()` → `async shouldShowAwarenessPopup()`
- `recordPopupShown()` → `async recordPopupShown()`
- `recordPopupDismissed()` → `async recordPopupDismissed()`
- `getTrackingStats()` → `async getTrackingStats()`
- `resetTrackingData()` → `async resetTrackingData()`

### 2. Authentication Utilities
**Before**: Login attempts and security logs in localStorage
**After**: Stored in `login_attempts` and `security_logs` database tables

**Changes**:
- `LoginAttemptTracker.recordAttempt()` → `async recordAttempt()`
- `LoginAttemptTracker.getAttempts()` → `async getAttempts()`
- `LoginAttemptTracker.isAccountLocked()` → `async isAccountLocked()`
- `SecurityLogger.logEvent()` → `async logEvent()`
- `SecurityLogger.getLogs()` → `async getLogs()`

### 3. API Service Token Management
**Before**: JWT tokens stored in localStorage
**After**: Secure HTTP-only cookies with database session management

**Changes**:
- Removed all client-side token storage
- All API requests now use `credentials: 'include'`
- Authentication handled via secure HTTP-only cookies
- Session management moved to server-side database

## Database Schema

### Tables Created
1. **user_sessions** - Session management and user state
2. **ad_blocker_tracking** - Ad blocker awareness popup tracking
3. **login_attempts** - Brute force protection and login monitoring
4. **security_logs** - Security event logging and audit trail
5. **auth_tokens** - Secure token management and validation

### Automatic Cleanup
- Daily cleanup procedure removes expired data
- Configurable retention periods for different data types
- Event scheduler handles automatic maintenance

## API Endpoints Added

### Tracking Endpoints (`/api/tracking/`)
- `GET /ad-blocker` - Get tracking record
- `POST /ad-blocker` - Save tracking record
- `POST /ad-blocker/shown` - Record popup shown
- `POST /ad-blocker/dismissed` - Record popup dismissed
- `DELETE /ad-blocker` - Reset tracking data

### Authentication Endpoints (`/api/auth/`)
- `POST /login-attempts` - Record login attempt
- `GET /login-attempts` - Get login attempts
- `DELETE /login-attempts` - Clear login attempts
- `GET /account-status` - Check account lock status
- `GET /unlock-time` - Get unlock time
- `POST /security-logs` - Log security event
- `GET /security-logs` - Get security logs
- `DELETE /security-logs` - Clear security logs

## Deployment Steps

### 1. Database Setup
```bash
# Run the migration setup script
cd server
node setup-localStorage-migration.js
```

### 2. Update Dependencies
```bash
# Install any missing dependencies
npm install
```

### 3. Environment Configuration
Ensure your `.env` file has proper database credentials:
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=streamdb_database
DB_USER=dbadmin_streamdb
DB_PASSWORD=your_password
DB_SOCKET=/var/run/mysqld/mysqld.sock  # For production
```

### 4. Server Restart
```bash
# Restart the server to load new routes
npm start
```

## Code Changes Required

### Frontend Components
Any components using the migrated functions need to handle async operations:

```javascript
// Before
if (shouldShowAwarenessPopup()) {
  // Show popup
}

// After
if (await shouldShowAwarenessPopup()) {
  // Show popup
}
```

### Error Handling
All database operations include fallback error handling:
- Network failures gracefully degrade
- Database connection issues don't crash the application
- Comprehensive logging for debugging

## Security Improvements

### 1. No Client-side Token Storage
- JWT tokens no longer stored in localStorage
- Secure HTTP-only cookies prevent XSS attacks
- Session management handled server-side

### 2. Enhanced Session Security
- Session data encrypted in database
- Automatic session expiration
- IP address and user agent tracking

### 3. Audit Trail
- Complete logging of security events
- Login attempt tracking with IP addresses
- Comprehensive user interaction monitoring

## Performance Considerations

### 1. Database Indexing
All tables include proper indexes for:
- Session ID lookups
- Timestamp-based queries
- User ID associations

### 2. Automatic Cleanup
- Expired sessions automatically removed
- Old logs cleaned up based on retention policies
- Database size managed automatically

### 3. Connection Pooling
- MySQL connection pooling for efficiency
- Proper connection management
- Error recovery and reconnection

## Testing

### 1. Functional Testing
- All existing functionality preserved
- Async operations properly handled
- Error scenarios gracefully managed

### 2. Security Testing
- No sensitive data in client-side storage
- Session security properly implemented
- Authentication flows working correctly

### 3. Performance Testing
- Database queries optimized
- Response times acceptable
- Memory usage stable

## Rollback Plan

If issues arise, you can temporarily revert by:
1. Keeping the old localStorage code as backup
2. Using feature flags to switch between implementations
3. Gradual migration of different components

## Monitoring

### 1. Database Monitoring
- Monitor table sizes and growth
- Check cleanup procedure execution
- Watch for connection issues

### 2. Application Monitoring
- Monitor API endpoint response times
- Check error rates for new endpoints
- Verify session management working correctly

### 3. Security Monitoring
- Monitor failed login attempts
- Check for unusual session patterns
- Verify audit logs are being created

## Benefits Achieved

1. **Enhanced Security**: No client-side storage of sensitive data
2. **Better User Experience**: Cross-device synchronization
3. **Improved Scalability**: Database-backed storage
4. **Comprehensive Auditing**: Full tracking of user interactions
5. **Production Ready**: Proper session management and cleanup
6. **Future Proof**: Foundation for advanced features

## Next Steps

1. Deploy to production environment
2. Monitor performance and adjust as needed
3. Implement additional security features
4. Add analytics and reporting capabilities
5. Consider implementing user accounts for enhanced features
