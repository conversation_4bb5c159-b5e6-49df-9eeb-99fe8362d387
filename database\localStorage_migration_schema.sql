-- ============================================================================
-- StreamDB localStorage Migration Schema
-- Replaces all localStorage usage with MySQL database tables
-- ============================================================================

-- User Sessions Table (replaces localStorage session management)
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NULL, -- NULL for anonymous sessions
    session_data JSON NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- Ad Blocker Awareness Tracking Table
CREATE TABLE IF NOT EXISTS ad_blocker_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NULL, -- For future user association
    last_shown_timestamp BIGINT NOT NULL DEFAULT 0,
    dismiss_count INT NOT NULL DEFAULT 0,
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_session (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_last_shown (last_shown_timestamp),
    INDEX idx_created_at (created_at)
);

-- Login Attempts Table (replaces localStorage login tracking)
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    username VARCHAR(255) NULL, -- Attempted username
    success BOOLEAN NOT NULL DEFAULT FALSE,
    failure_reason VARCHAR(100) NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_session_id (session_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_timestamp (timestamp),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
);

-- Security Event Logs Table (replaces localStorage security logs)
CREATE TABLE IF NOT EXISTS security_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NULL,
    event_type ENUM(
        'LOGIN_SUCCESS', 'LOGIN_FAILED', 'LOGOUT', 'SESSION_EXPIRED',
        'SESSION_REFRESHED', 'ACCOUNT_LOCKED', 'SECURITY_VIOLATION'
    ) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_timestamp (timestamp),
    INDEX idx_created_at (created_at)
);

-- Authentication Tokens Table (replaces localStorage token storage)
CREATE TABLE IF NOT EXISTS auth_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token_hash VARCHAR(255) NOT NULL, -- Hashed token for security
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    token_type ENUM('access', 'refresh') NOT NULL DEFAULT 'access',
    expires_at TIMESTAMP NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_token_hash (token_hash),
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_token_type (token_type),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_revoked (is_revoked)
);

-- Session Cleanup Procedure (removes expired sessions and tokens)
DELIMITER //
CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    -- Remove expired user sessions
    DELETE FROM user_sessions WHERE expires_at < NOW();
    
    -- Remove expired auth tokens
    DELETE FROM auth_tokens WHERE expires_at < NOW();
    
    -- Remove old login attempts (older than 7 days)
    DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Remove old security logs (older than 30 days)
    DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Remove old ad blocker tracking (older than 90 days)
    DELETE FROM ad_blocker_tracking WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
END //
DELIMITER ;

-- Create event to run cleanup daily
CREATE EVENT IF NOT EXISTS daily_session_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredSessions();

-- Enable event scheduler if not already enabled
SET GLOBAL event_scheduler = ON;
