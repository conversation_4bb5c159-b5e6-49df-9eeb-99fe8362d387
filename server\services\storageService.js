/**
 * Storage Service - Database replacement for localStorage operations
 * Handles all database operations for session management, tracking, and authentication
 */

const db = require('../config/database');
const crypto = require('crypto');

// Safe database wrapper to handle connection failures
const safeDatabase = {
  async execute(query, params = []) {
    try {
      return await db.execute(query, params);
    } catch (error) {
      console.error('Database operation failed:', error.message);
      console.error('Query:', query);
      console.error('Params:', params);
      throw error;
    }
  },

  async executeTransaction(queries) {
    try {
      return await db.executeTransaction(queries);
    } catch (error) {
      console.error('Database transaction failed:', error.message);
      throw error;
    }
  }
};

/**
 * Session Management Service
 */
class SessionService {
  /**
   * Create a new session
   */
  static async createSession(sessionId, sessionData, expiresAt, userAgent = null, ipAddress = null, userId = null) {
    const query = `
      INSERT INTO user_sessions (id, user_id, session_data, ip_address, user_agent, expires_at)
      VALUES (?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        session_data = VALUES(session_data),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const params = [sessionId, userId, JSON.stringify(sessionData), ipAddress, userAgent, expiresAt];
    await safeDatabase.execute(query, params);
    return true;
  }

  /**
   * Get session by ID
   */
  static async getSession(sessionId) {
    const query = `
      SELECT id, user_id, session_data, ip_address, user_agent, expires_at, created_at, updated_at
      FROM user_sessions
      WHERE id = ? AND expires_at > NOW()
    `;
    
    const [rows] = await safeDatabase.execute(query, [sessionId]);
    if (rows.length === 0) return null;
    
    const session = rows[0];
    return {
      ...session,
      session_data: JSON.parse(session.session_data)
    };
  }

  /**
   * Update session data
   */
  static async updateSession(sessionId, sessionData, expiresAt = null) {
    let query, params;
    
    if (expiresAt) {
      query = `
        UPDATE user_sessions 
        SET session_data = ?, expires_at = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      params = [JSON.stringify(sessionData), expiresAt, sessionId];
    } else {
      query = `
        UPDATE user_sessions 
        SET session_data = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      params = [JSON.stringify(sessionData), sessionId];
    }
    
    const [result] = await safeDatabase.execute(query, params);
    return result.affectedRows > 0;
  }

  /**
   * Delete session
   */
  static async deleteSession(sessionId) {
    const query = `DELETE FROM user_sessions WHERE id = ?`;
    const [result] = await safeDatabase.execute(query, [sessionId]);
    return result.affectedRows > 0;
  }

  /**
   * Clean up expired sessions
   */
  static async cleanupExpiredSessions() {
    const query = `DELETE FROM user_sessions WHERE expires_at < NOW()`;
    const [result] = await safeDatabase.execute(query);
    return result.affectedRows;
  }
}

/**
 * Ad Blocker Tracking Service
 */
class AdBlockerTrackingService {
  /**
   * Get tracking record by session ID
   */
  static async getTrackingRecord(sessionId) {
    const query = `
      SELECT session_id, user_id, last_shown_timestamp, dismiss_count, user_agent, ip_address, created_at, updated_at
      FROM ad_blocker_tracking
      WHERE session_id = ?
    `;
    
    const [rows] = await safeDatabase.execute(query, [sessionId]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * Create or update tracking record
   */
  static async saveTrackingRecord(sessionId, data, userAgent = null, ipAddress = null, userId = null) {
    const query = `
      INSERT INTO ad_blocker_tracking (session_id, user_id, last_shown_timestamp, dismiss_count, user_agent, ip_address)
      VALUES (?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        last_shown_timestamp = VALUES(last_shown_timestamp),
        dismiss_count = VALUES(dismiss_count),
        user_agent = VALUES(user_agent),
        ip_address = VALUES(ip_address),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const params = [
      sessionId,
      userId,
      data.lastShownTimestamp || 0,
      data.dismissCount || 0,
      userAgent,
      ipAddress
    ];
    
    await safeDatabase.execute(query, params);
    return true;
  }

  /**
   * Record popup shown
   */
  static async recordPopupShown(sessionId, userAgent = null, ipAddress = null) {
    const now = Date.now();
    const query = `
      INSERT INTO ad_blocker_tracking (session_id, last_shown_timestamp, dismiss_count, user_agent, ip_address)
      VALUES (?, ?, 0, ?, ?)
      ON DUPLICATE KEY UPDATE
        last_shown_timestamp = VALUES(last_shown_timestamp),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    await safeDatabase.execute(query, [sessionId, now, userAgent, ipAddress]);
    return true;
  }

  /**
   * Record popup dismissed
   */
  static async recordPopupDismissed(sessionId, userAgent = null, ipAddress = null) {
    const query = `
      INSERT INTO ad_blocker_tracking (session_id, last_shown_timestamp, dismiss_count, user_agent, ip_address)
      VALUES (?, 0, 1, ?, ?)
      ON DUPLICATE KEY UPDATE
        dismiss_count = dismiss_count + 1,
        updated_at = CURRENT_TIMESTAMP
    `;
    
    await safeDatabase.execute(query, [sessionId, userAgent, ipAddress]);
    return true;
  }

  /**
   * Reset tracking data
   */
  static async resetTrackingData(sessionId) {
    const query = `DELETE FROM ad_blocker_tracking WHERE session_id = ?`;
    const [result] = await safeDatabase.execute(query, [sessionId]);
    return result.affectedRows > 0;
  }
}

/**
 * Login Attempts Service
 */
class LoginAttemptsService {
  /**
   * Record login attempt
   */
  static async recordAttempt(sessionId, success, username = null, failureReason = null, userAgent = null, ipAddress = null) {
    const query = `
      INSERT INTO login_attempts (session_id, ip_address, user_agent, username, success, failure_reason, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [sessionId, ipAddress, userAgent, username, success, failureReason, Date.now()];
    await safeDatabase.execute(query, params);
    return true;
  }

  /**
   * Get recent login attempts for session
   */
  static async getAttempts(sessionId, hoursBack = 1) {
    const timeThreshold = Date.now() - (hoursBack * 60 * 60 * 1000);
    const query = `
      SELECT session_id, ip_address, user_agent, username, success, failure_reason, timestamp, created_at
      FROM login_attempts
      WHERE session_id = ? AND timestamp > ?
      ORDER BY timestamp DESC
    `;
    
    const [rows] = await safeDatabase.execute(query, [sessionId, timeThreshold]);
    return rows;
  }

  /**
   * Clear login attempts for session
   */
  static async clearAttempts(sessionId) {
    const query = `DELETE FROM login_attempts WHERE session_id = ?`;
    const [result] = await safeDatabase.execute(query, [sessionId]);
    return result.affectedRows;
  }

  /**
   * Check if account should be locked
   */
  static async isAccountLocked(sessionId, maxAttempts = 5, lockoutDuration = 15 * 60 * 1000) {
    const timeThreshold = Date.now() - lockoutDuration;
    const query = `
      SELECT COUNT(*) as failed_count
      FROM login_attempts
      WHERE session_id = ? AND success = FALSE AND timestamp > ?
    `;
    
    const [rows] = await safeDatabase.execute(query, [sessionId, timeThreshold]);
    return rows[0].failed_count >= maxAttempts;
  }
}

/**
 * Security Logs Service
 */
class SecurityLogsService {
  /**
   * Log security event
   */
  static async logEvent(sessionId, eventType, severity = 'medium', details = null, userId = null, userAgent = null, ipAddress = null) {
    const query = `
      INSERT INTO security_logs (session_id, user_id, event_type, severity, details, ip_address, user_agent, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      sessionId,
      userId,
      eventType,
      severity,
      details ? JSON.stringify(details) : null,
      ipAddress,
      userAgent,
      Date.now()
    ];

    await safeDatabase.execute(query, params);
    return true;
  }

  /**
   * Get security logs for session
   */
  static async getLogs(sessionId, limit = 100) {
    const query = `
      SELECT session_id, user_id, event_type, severity, details, ip_address, user_agent, timestamp, created_at
      FROM security_logs
      WHERE session_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `;

    const [rows] = await safeDatabase.execute(query, [sessionId, limit]);
    return rows.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  /**
   * Clear security logs for session
   */
  static async clearLogs(sessionId) {
    const query = `DELETE FROM security_logs WHERE session_id = ?`;
    const [result] = await safeDatabase.execute(query, [sessionId]);
    return result.affectedRows;
  }
}

/**
 * Auth Tokens Service
 */
class AuthTokensService {
  /**
   * Hash token for secure storage
   */
  static hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Store auth token
   */
  static async storeToken(token, sessionId, userId, tokenType = 'access', expiresAt, userAgent = null, ipAddress = null) {
    const tokenHash = this.hashToken(token);
    const query = `
      INSERT INTO auth_tokens (token_hash, session_id, user_id, token_type, expires_at, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [tokenHash, sessionId, userId, tokenType, expiresAt, ipAddress, userAgent];
    await safeDatabase.execute(query, params);
    return true;
  }

  /**
   * Verify token
   */
  static async verifyToken(token) {
    const tokenHash = this.hashToken(token);
    const query = `
      SELECT token_hash, session_id, user_id, token_type, expires_at, is_revoked, created_at
      FROM auth_tokens
      WHERE token_hash = ? AND expires_at > NOW() AND is_revoked = FALSE
    `;

    const [rows] = await safeDatabase.execute(query, [tokenHash]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * Revoke token
   */
  static async revokeToken(token) {
    const tokenHash = this.hashToken(token);
    const query = `
      UPDATE auth_tokens
      SET is_revoked = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE token_hash = ?
    `;

    const [result] = await safeDatabase.execute(query, [tokenHash]);
    return result.affectedRows > 0;
  }

  /**
   * Revoke all tokens for session
   */
  static async revokeSessionTokens(sessionId) {
    const query = `
      UPDATE auth_tokens
      SET is_revoked = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE session_id = ?
    `;

    const [result] = await safeDatabase.execute(query, [sessionId]);
    return result.affectedRows;
  }

  /**
   * Clean up expired tokens
   */
  static async cleanupExpiredTokens() {
    const query = `DELETE FROM auth_tokens WHERE expires_at < NOW()`;
    const [result] = await safeDatabase.execute(query);
    return result.affectedRows;
  }
}

module.exports = {
  SessionService,
  AdBlockerTrackingService,
  LoginAttemptsService,
  SecurityLogsService,
  AuthTokensService,
  safeDatabase
};
