#!/usr/bin/env node

/**
 * StreamDB Online - Deployment Database Initialization
 * 
 * This script initializes the database tables required for GitHub webhook
 * deployment tracking and admin panel integration.
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Load environment variables from the correct path
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  multipleStatements: true
};

// Add socket path if specified (for local connections)
if (process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  delete dbConfig.host;
  delete dbConfig.port;
}

async function initializeDeploymentDatabase() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    console.log('Database:', dbConfig.database);
    console.log('User:', dbConfig.user);
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, '../database/deployment-schema.sql');
    console.log('📄 Reading schema file:', schemaPath);
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found: ${schemaPath}`);
    }
    
    const schema = fs.readFileSync(schemaPath, 'utf8');
    console.log('✅ Schema file loaded');
    
    // Execute the schema
    console.log('🏗️ Creating deployment tables and views...');
    await connection.execute(schema);
    console.log('✅ Deployment schema created successfully');
    
    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME IN ('deployments', 'deployment_logs', 'webhook_events', 'manual_deployments', 'deployment_config')
    `, [dbConfig.database]);
    
    console.log('📊 Created tables:');
    tables.forEach(table => {
      console.log(`  ✅ ${table.TABLE_NAME}`);
    });
    
    // Check if all required tables exist
    const requiredTables = ['deployments', 'deployment_logs', 'webhook_events', 'manual_deployments', 'deployment_config'];
    const createdTableNames = tables.map(t => t.TABLE_NAME);
    const missingTables = requiredTables.filter(table => !createdTableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.warn('⚠️ Some tables were not created:', missingTables);
    } else {
      console.log('✅ All required tables created successfully');
    }
    
    // Verify views were created
    const [views] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.VIEWS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME IN ('recent_deployments', 'deployment_stats', 'recent_logs')
    `, [dbConfig.database]);
    
    console.log('📊 Created views:');
    views.forEach(view => {
      console.log(`  ✅ ${view.TABLE_NAME}`);
    });
    
    // Test deployment config
    console.log('🧪 Testing deployment configuration...');
    const [config] = await connection.execute('SELECT * FROM deployment_config');
    console.log(`✅ Deployment config loaded: ${config.length} settings`);
    
    // Insert a test log entry
    console.log('🧪 Testing deployment logs...');
    await connection.execute(
      'INSERT INTO deployment_logs (level, message, data) VALUES (?, ?, ?)',
      ['INFO', 'Database initialization completed', JSON.stringify({ 
        timestamp: new Date().toISOString(),
        tables_created: createdTableNames.length,
        views_created: views.length
      })]
    );
    console.log('✅ Test log entry created');
    
    console.log('\n🎉 Deployment database initialization completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`  • Tables created: ${createdTableNames.length}`);
    console.log(`  • Views created: ${views.length}`);
    console.log(`  • Configuration entries: ${config.length}`);
    console.log('\n🚀 Your webhook deployment system is ready to use!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Troubleshooting:');
      console.error('  • Check if MySQL server is running');
      console.error('  • Verify database credentials in .env file');
      console.error('  • Ensure database exists and user has proper permissions');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n🔧 Troubleshooting:');
      console.error('  • Check database username and password in .env file');
      console.error('  • Verify user has CREATE, INSERT, UPDATE, DELETE permissions');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('\n🔧 Troubleshooting:');
      console.error('  • Check if database name is correct in .env file');
      console.error('  • Ensure database exists on MySQL server');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the initialization
if (require.main === module) {
  initializeDeploymentDatabase();
}

module.exports = { initializeDeploymentDatabase };
