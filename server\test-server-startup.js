#!/usr/bin/env node

/**
 * StreamDB Online - Server Startup Test
 * Test if the server can start properly without database connection
 */

const path = require('path');

// Mock database connection for testing
const originalRequire = require;
require = function(id) {
  if (id === './config/database') {
    return {
      executeQuery: async () => [],
      testConnection: async () => true
    };
  }
  return originalRequire.apply(this, arguments);
};

// Set test environment
process.env.NODE_ENV = 'test';
process.env.PORT = '3002'; // Use different port for testing

console.log('🧪 Testing server startup...');

try {
  // Load the server
  const app = require('./index');
  
  console.log('✅ Server loaded successfully');
  console.log('✅ All modules imported correctly');
  console.log('✅ No syntax errors detected');
  
  // Test would normally start server, but we'll just verify loading
  setTimeout(() => {
    console.log('🏁 Server startup test completed successfully!');
    process.exit(0);
  }, 1000);
  
} catch (error) {
  console.error('❌ Server startup test failed:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
